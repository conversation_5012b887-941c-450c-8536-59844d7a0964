import { analyticsEvent } from '@analytics';
import { SupportedCountries, useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings, useLanguage } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { RegistrationProvider, Screens } from '@bp/registration-mfe';
import { LoadingSpinner } from '@components/LoadingSpinner/LoadingSpinner';
import env from '@env';
import { useConfig } from '@providers/ConfigProvider';
import { useReturnNavigation } from '@providers/ReturnNavigationProvider';
import { logger } from '@utils/logger';
import { navigate, navigation } from '@utils/navigation';
import { poll } from '@utils/poll';
import React, { useCallback, useEffect, useState } from 'react';

// Function to map country codes to SupportedCountries
const mapCountryToSupportedCountry = (country: string): SupportedCountries => {
  if (country === 'GB') {
    return 'UK' as SupportedCountries;
  }
  return country as SupportedCountries;
};

const Render = () => {
  const authContext = useAuth();
  const { loginOrRegister, logout, getUser, authenticated } = authContext;
  const { language } = useLanguage();
  const { returnParams, returnNavigate } = useReturnNavigation();
  const { getStatus, onboardAccount } = useOnboarding();
  const {
    external_links: ExternalLinks,
    brandCountries,
    registration_mfe: { enableAralActivationMessage },
  } = useConfig();
  const { refetchUserInfo } = useAppSettings();

  const [onboardingComplete, setOnboardingComplete] = useState<
    undefined | boolean
  >();
  const [onboardingFailed, setOnboardingFailed] = useState(false);

  // Fetch and initialize users onboarding status once authenticated
  useEffect(() => {
    if (authenticated) {
      const initializeOnboardingStatus = async () => {
        const onboardingStatus = await getStatus();
        setOnboardingComplete(!!onboardingStatus.account);
      };
      initializeOnboardingStatus();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (onboardingComplete) {
      refetchUserInfo();
    }
  }, [onboardingComplete, refetchUserInfo]);

  const onExit = useCallback(() => {
    if (navigation.getCurrentRoute()?.name !== 'MapMFE.MapScreen') {
      navigate('Tabs', returnParams, true);
    }
  }, [returnParams]);

  const onLogin = useCallback(() => {
    loginOrRegister(language);
  }, [loginOrRegister, language]);

  const onLogout = () => {
    setOnboardingFailed(false);
    logout();
  };

  const onUserIdCreated = (userId: string | null) => {
    logger.info(`[Registration]: onUserIdCreated called with ${userId}`);
  };

  const onRegistrationCompleted = async () => {
    // Refresh customer data on registration completion
    await getUser();
    refetchUserInfo();
    try {
      if (returnParams) {
        return returnNavigate();
      }
      return navigate('Tabs', { screen: 'Map' }, true);
    } catch (err) {
      logger.error(`[Registration]: error onboarding user ${err}`);
      return navigate('Tabs', { screen: 'Map' }, true);
    }
  };

  const onEmailVerificationCompleted = (verifiedEmail: string) => {
    logger.info(
      `[Registration]: onEmailVerificationCompleted called with ${verifiedEmail}`,
    );
    getUser();
  };

  const pollForOnboardingStatus = async () => {
    return poll({
      fn: getStatus,
      fnCondition: (status: { account: boolean }) => !status.account,
      ms: 5000,
      retryLimit: 11,
    });
  };

  const onInitiateOnboarding = async () => {
    logger.info('[Registration]: onInitiateOnboarding called');
    setOnboardingFailed(false);

    // Refetch latest onboarding status
    const onboardingStatus = await getStatus();
    if (onboardingStatus.account) {
      setOnboardingComplete(true);
      return;
    }

    // Refetch latest user details
    const updatedUser = await getUser();
    if (!updatedUser) {
      setOnboardingFailed(true);
      return;
    }

    // Safeguard against unverified emails attempting to onboard with BPCM
    const { country, email, emailVerified, firstName, lastName } = updatedUser;
    if (!emailVerified) {
      setOnboardingFailed(true);
    }

    // Attempt to onboard account
    const onboardAccountResponse = await onboardAccount({
      firstName,
      lastName,
      email,
      homeCountry: mapCountryToSupportedCountry(country),
    });

    if (onboardAccountResponse.success) {
      const onboardingStatusPollResponse = await pollForOnboardingStatus();

      if (onboardingStatusPollResponse?.account) {
        setOnboardingComplete(true);
        return;
      }
      setOnboardingFailed(true);
      return;
    }

    setOnboardingFailed(true);
  };

  // Ensure onboarding status flag has been initialized before loading MFE
  if (onboardingComplete === undefined) {
    return <LoadingSpinner />;
  }

  return (
    <RegistrationProvider
      authContext={authContext}
      // @ts-ignore
      brand={env.BRAND}
      externalLinks={{ de_pricing_link: ExternalLinks.de_pricing_link }}
      featureFlags={{
        enableSpain: true,
        enableCountrySelection: true,
        brandCountries,
        enableAralActivationMessage,
        enableNewMarketingConsents: true,
      }}
      locale={language}
      navigation={navigation}
      privacyPolicy={ExternalLinks.emsp_privacy_policy_link}
      termsAndConditions={ExternalLinks.emsp_tcs_link}
      onboardingComplete={onboardingComplete}
      onboardingFailed={onboardingFailed}
      onAnalyticsEvent={analyticsEvent}
      onEmailVerificationCompleted={onEmailVerificationCompleted}
      onExitMFE={onExit}
      onLogin={onLogin}
      onLogout={onLogout}
      onRegistrationCompleted={onRegistrationCompleted}
      onUserIdCreated={onUserIdCreated}
      onInitiateOnboarding={onInitiateOnboarding}>
      <Screens />
    </RegistrationProvider>
  );
};
export default Render;
